# AI-Stethic MVP

A VS Code extension that enforces design system consistency at both the token level (colors, typography, spacing, border-radius) and component level for React projects.

## 🎯 Features

### ✅ MVP Features (Implemented)

- **🎨 Design Token Wizard**: Automatically scan your codebase or import existing tokens
- **🔍 Token-Level Linting**: Detect off-brand colors, spacing, typography, and border-radius values
- **⚛️ Component Registration**: Register and enforce usage of official React components
- **🛠️ Quick Fixes**: One-click fixes for token violations and component issues
- **🚀 Enhance Component**: Stub API for future AI-driven styling enhancements
- **⚙️ Configurable**: Customize linting rules and thresholds

### 🔮 Future Features (Planned)

- Real AI-powered component enhancement
- Image-based token detection
- Multi-editor support (Windsurf, Cursor)
- CI/CD integration
- Advanced component analysis

## 📦 Installation

### Prerequisites

- VS Code 1.74.0 or higher
- Node.js 18.0.0 or higher
- A React/TypeScript project

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-stethic-mvp
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build all packages**
   ```bash
   npm run build
   ```

4. **Install the VS Code extension**
   ```bash
   cd packages/vscode-extension
   npm run package
   # Install the generated .vsix file in VS Code
   ```

5. **Start the stub backend (for enhancement features)**
   ```bash
   cd stub-backend
   npm install
   npm start
   ```

## 🚀 Quick Start

### 1. Initialize Design Tokens

1. Open your React project in VS Code
2. Run command: `AI-Stethic: Initialize Design Tokens`
3. Choose your initialization method:
   - **Scan Codebase**: Automatically detect tokens from existing files
   - **Import JSON**: Import from an existing tokens file
   - **Start Blank**: Create an empty tokens file

### 2. Register Components

1. Run command: `AI-Stethic: Register Components`
2. Select components from your `src/components/ui` directory
3. Configure required props and CSS classes for each component

### 3. Start Linting

Once tokens and components are configured, the extension will automatically:
- Highlight token violations in CSS/SCSS files
- Flag component usage issues in JSX/TSX files
- Provide quick fixes for common problems

## 📁 Project Structure

```
ai-stethic-mvp/
├── packages/
│   ├── design-linter/          # Core linting engine
│   │   ├── src/
│   │   │   ├── index.ts        # Main DesignLinter class
│   │   │   ├── css-parser.ts   # CSS/SCSS parsing
│   │   │   ├── jsx-parser.ts   # JSX/TSX parsing
│   │   │   ├── token-matcher.ts # Token clustering
│   │   │   └── types.ts        # Type definitions
│   │   └── tokens.example.json # Example tokens file
│   └── vscode-extension/       # VS Code extension
│       ├── src/
│       │   ├── extension.ts    # Main extension entry
│       │   ├── providers/      # Diagnostic & code action providers
│       │   ├── wizards/        # Token & component wizards
│       │   └── commands/       # Extension commands
│       └── package.json        # Extension manifest
├── stub-backend/               # Enhancement API stub
│   └── src/server.ts          # Express server
└── README.md
```

## ⚙️ Configuration

Configure AI-Stethic through VS Code settings:

```json
{
  "aiStethic.tokensFile": ".ai-stethic/tokens.json",
  "aiStethic.enableLinter": true,
  "aiStethic.enableTokenLinting": true,
  "aiStethic.enableComponentLinting": true,
  "aiStethic.colorThreshold": 10,
  "aiStethic.spacingThreshold": 2,
  "aiStethic.componentDirectory": "src/components/ui",
  "aiStethic.enhanceApiUrl": "http://localhost:8000/enhance"
}
```

## 📋 Example Tokens File

```json
{
  "colors": [
    {
      "name": "primary",
      "value": "#3b82f6",
      "type": "color",
      "hex": "#3b82f6"
    }
  ],
  "spacing": [
    {
      "name": "md",
      "value": "16px",
      "type": "spacing",
      "pixels": 16,
      "unit": "px"
    }
  ],
  "components": {
    "Button": {
      "name": "Button",
      "importPath": "./src/components/ui/Button.tsx",
      "requiredProps": ["variant"],
      "requiredClasses": ["btn"]
    }
  }
}
```

## 🔧 Commands

- `AI-Stethic: Initialize Design Tokens` - Set up your design tokens
- `AI-Stethic: Register Components` - Register React components
- `AI-Stethic: Enhance Component` - Enhance selected code (requires backend)
- `AI-Stethic: Add New Token` - Add a new token from selected value

## 🐛 Troubleshooting

### Extension Not Working

1. Check that you have a workspace open
2. Verify your tokens file exists and is valid JSON
3. Check the VS Code output panel for errors

### Enhancement Feature Not Working

1. Make sure the stub backend is running on port 8000
2. Check the `aiStethic.enhanceApiUrl` setting
3. Verify network connectivity to the backend

### Linting Not Detecting Issues

1. Check that linting is enabled in settings
2. Verify your tokens file contains the expected tokens
3. Make sure you're working with supported file types (CSS, SCSS, JSX, TSX)

## 🧪 Testing

### Design Linter Core

```bash
cd packages/design-linter
npm test
```

### VS Code Extension

1. Open the extension project in VS Code
2. Press F5 to launch Extension Development Host
3. Test features in the development environment

### Stub Backend

```bash
cd stub-backend
npm start
# Test with curl or Postman
curl -X POST http://localhost:8000/enhance \
  -H "Content-Type: application/json" \
  -d '{"code": "<Button>Test</Button>", "styleHint": "make it modern"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🗺️ Roadmap

### Phase 2 (Next)
- Real AI integration for component enhancement
- Advanced component analysis
- Performance optimizations
- Better error handling

### Phase 3 (Future)
- Image-based token detection
- Multi-editor support
- CI/CD integration
- Team collaboration features

---

**Note**: This is an MVP implementation focused on demonstrating core functionality. The enhancement feature uses a stub backend that simulates AI responses.
