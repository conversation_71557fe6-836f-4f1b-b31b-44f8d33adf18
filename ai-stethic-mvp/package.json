{"name": "ai-stethic-mvp", "version": "0.1.0", "private": true, "description": "AI-Stethic MVP: VS Code extension for design system consistency enforcement", "workspaces": ["packages/*", "stub-backend"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "clean": "rm -rf packages/*/dist packages/*/node_modules stub-backend/dist stub-backend/node_modules node_modules", "dev:backend": "npm run dev --workspace=stub-backend", "build:extension": "npm run build --workspace=packages/vscode-extension", "test:linter": "npm run test --workspace=packages/design-linter"}, "devDependencies": {"typescript": "^5.3.0", "@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ai-stethic-mvp.git"}, "keywords": ["vscode-extension", "design-system", "linting", "react", "css", "tokens"], "author": "Your Name", "license": "MIT"}