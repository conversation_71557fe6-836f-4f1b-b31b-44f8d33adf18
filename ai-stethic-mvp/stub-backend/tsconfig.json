{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "sourceMap": true, "resolveJsonModule": true, "moduleResolution": "node", "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}