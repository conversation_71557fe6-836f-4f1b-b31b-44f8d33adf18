{"colors": [{"name": "primary", "value": "#3b82f6", "type": "color", "hex": "#3b82f6", "rgb": {"r": 59, "g": 130, "b": 246}, "hsl": {"h": 217, "s": 0.91, "l": 0.6}}, {"name": "primary-dark", "value": "#1d4ed8", "type": "color", "hex": "#1d4ed8", "rgb": {"r": 29, "g": 78, "b": 216}, "hsl": {"h": 224, "s": 0.76, "l": 0.48}}, {"name": "secondary", "value": "#64748b", "type": "color", "hex": "#64748b", "rgb": {"r": 100, "g": 116, "b": 139}, "hsl": {"h": 215, "s": 0.16, "l": 0.47}}, {"name": "success", "value": "#10b981", "type": "color", "hex": "#10b981", "rgb": {"r": 16, "g": 185, "b": 129}, "hsl": {"h": 160, "s": 0.84, "l": 0.39}}, {"name": "warning", "value": "#f59e0b", "type": "color", "hex": "#f59e0b", "rgb": {"r": 245, "g": 158, "b": 11}, "hsl": {"h": 38, "s": 0.92, "l": 0.5}}, {"name": "error", "value": "#ef4444", "type": "color", "hex": "#ef4444", "rgb": {"r": 239, "g": 68, "b": 68}, "hsl": {"h": 0, "s": 0.84, "l": 0.6}}, {"name": "background", "value": "#ffffff", "type": "color", "hex": "#ffffff", "rgb": {"r": 255, "g": 255, "b": 255}, "hsl": {"h": 0, "s": 0, "l": 1}}, {"name": "background-secondary", "value": "#f8fafc", "type": "color", "hex": "#f8fafc", "rgb": {"r": 248, "g": 250, "b": 252}, "hsl": {"h": 210, "s": 0.4, "l": 0.98}}, {"name": "text-primary", "value": "#1e293b", "type": "color", "hex": "#1e293b", "rgb": {"r": 30, "g": 41, "b": 59}, "hsl": {"h": 217, "s": 0.32, "l": 0.17}}, {"name": "text-secondary", "value": "#64748b", "type": "color", "hex": "#64748b", "rgb": {"r": 100, "g": 116, "b": 139}, "hsl": {"h": 215, "s": 0.16, "l": 0.47}}], "spacing": [{"name": "xs", "value": "4px", "type": "spacing", "pixels": 4, "unit": "px"}, {"name": "sm", "value": "8px", "type": "spacing", "pixels": 8, "unit": "px"}, {"name": "md", "value": "16px", "type": "spacing", "pixels": 16, "unit": "px"}, {"name": "lg", "value": "24px", "type": "spacing", "pixels": 24, "unit": "px"}, {"name": "xl", "value": "32px", "type": "spacing", "pixels": 32, "unit": "px"}, {"name": "2xl", "value": "48px", "type": "spacing", "pixels": 48, "unit": "px"}, {"name": "3xl", "value": "64px", "type": "spacing", "pixels": 64, "unit": "px"}], "typography": [{"name": "font-sans", "value": "Inter, system-ui, sans-serif", "type": "typography", "fontFamily": "Inter, system-ui, sans-serif"}, {"name": "font-mono", "value": "Fira Code, monospace", "type": "typography", "fontFamily": "Fira Code, monospace"}, {"name": "text-xs", "value": "12px", "type": "typography", "fontSize": "12px"}, {"name": "text-sm", "value": "14px", "type": "typography", "fontSize": "14px"}, {"name": "text-base", "value": "16px", "type": "typography", "fontSize": "16px"}, {"name": "text-lg", "value": "18px", "type": "typography", "fontSize": "18px"}, {"name": "text-xl", "value": "20px", "type": "typography", "fontSize": "20px"}, {"name": "text-2xl", "value": "24px", "type": "typography", "fontSize": "24px"}, {"name": "text-3xl", "value": "30px", "type": "typography", "fontSize": "30px"}], "borderRadius": [{"name": "rounded-none", "value": "0px", "type": "borderRadius", "pixels": 0, "unit": "px"}, {"name": "rounded-sm", "value": "2px", "type": "borderRadius", "pixels": 2, "unit": "px"}, {"name": "rounded", "value": "4px", "type": "borderRadius", "pixels": 4, "unit": "px"}, {"name": "rounded-md", "value": "6px", "type": "borderRadius", "pixels": 6, "unit": "px"}, {"name": "rounded-lg", "value": "8px", "type": "borderRadius", "pixels": 8, "unit": "px"}, {"name": "rounded-xl", "value": "12px", "type": "borderRadius", "pixels": 12, "unit": "px"}, {"name": "rounded-2xl", "value": "16px", "type": "borderRadius", "pixels": 16, "unit": "px"}, {"name": "rounded-full", "value": "9999px", "type": "borderRadius", "pixels": 9999, "unit": "px"}], "components": {"Button": {"name": "<PERSON><PERSON>", "importPath": "./src/components/ui/Button.tsx", "requiredProps": ["variant"], "requiredClasses": ["btn"]}, "Card": {"name": "Card", "importPath": "./src/components/ui/Card.tsx", "requiredProps": [], "requiredClasses": ["card"]}, "Modal": {"name": "Modal", "importPath": "./src/components/ui/Modal.tsx", "requiredProps": ["isOpen", "onClose"], "requiredClasses": []}, "Input": {"name": "Input", "importPath": "./src/components/ui/Input.tsx", "requiredProps": ["type"], "requiredClasses": ["input"]}, "Badge": {"name": "Badge", "importPath": "./src/components/ui/Badge.tsx", "requiredProps": ["variant"], "requiredClasses": ["badge"]}}}