{"name": "@ai-stethic/design-linter", "version": "0.1.0", "description": "Core design system linting engine for AI-Stethic", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "node dist/test.js", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "dev": "tsc --watch"}, "dependencies": {"postcss": "^8.4.32", "postcss-safe-parser": "^7.0.0", "@babel/parser": "^7.23.6", "@babel/traverse": "^7.23.6", "@babel/types": "^7.23.6", "tinycolor2": "^1.6.0"}, "devDependencies": {"@types/babel__parser": "^7.1.1", "@types/babel__traverse": "^7.20.4", "@types/tinycolor2": "^1.4.6", "@types/node": "^20.0.0", "typescript": "^5.3.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}, "files": ["dist", "tokens.example.json"], "keywords": ["design-tokens", "css-linting", "jsx-linting", "design-system"], "author": "Your Name", "license": "MIT"}