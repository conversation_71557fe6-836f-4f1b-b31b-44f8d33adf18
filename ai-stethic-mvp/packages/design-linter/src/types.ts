/**
 * Core types for AI-Stethic design system linting
 */

export interface DesignToken {
  name: string;
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'borderRadius';
}

export interface ColorToken extends DesignToken {
  type: 'color';
  hex: string;
  rgb?: { r: number; g: number; b: number };
  hsl?: { h: number; s: number; l: number };
}

export interface SpacingToken extends DesignToken {
  type: 'spacing';
  pixels: number;
  unit: 'px' | 'rem' | 'em' | '%';
}

export interface TypographyToken extends DesignToken {
  type: 'typography';
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  lineHeight?: string;
}

export interface BorderRadiusToken extends DesignToken {
  type: 'borderRadius';
  pixels: number;
  unit: 'px' | 'rem' | 'em';
}

export interface ComponentDefinition {
  name: string;
  importPath: string;
  requiredProps?: string[];
  requiredClasses?: string[];
}

export interface TokensFile {
  colors: ColorToken[];
  spacing: SpacingToken[];
  typography: TypographyToken[];
  borderRadius: BorderRadiusToken[];
  components: Record<string, ComponentDefinition>;
}

export interface LintViolation {
  type: 'color' | 'spacing' | 'typography' | 'borderRadius' | 'component';
  message: string;
  line: number;
  column: number;
  length: number;
  severity: 'error' | 'warning' | 'info';
  suggestion?: string;
  quickFix?: QuickFix;
}

export interface QuickFix {
  type: 'replace' | 'add' | 'remove' | 'import';
  newText: string;
  range?: {
    start: { line: number; column: number };
    end: { line: number; column: number };
  };
}

export interface LintOptions {
  tokensFile?: string;
  enableTokenLinting?: boolean;
  enableComponentLinting?: boolean;
  colorThreshold?: number; // For color similarity matching
  spacingThreshold?: number; // For spacing similarity matching
}

export interface ParsedCSSRule {
  property: string;
  value: string;
  line: number;
  column: number;
}

export interface ParsedJSXElement {
  tagName: string;
  props: Record<string, string>;
  line: number;
  column: number;
  importPath?: string;
}

export interface ComponentViolation extends LintViolation {
  type: 'component';
  componentName: string;
  violationType: 'missing-import' | 'wrong-import' | 'missing-prop' | 'missing-class' | 'duplicate-definition';
}

export interface TokenCandidate {
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'borderRadius';
  occurrences: Array<{
    file: string;
    line: number;
    column: number;
  }>;
  suggestedName?: string;
}
