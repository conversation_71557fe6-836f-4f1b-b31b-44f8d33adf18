import * as fs from 'fs';
import * as path from 'path';
import { CSSParser } from './css-parser';
import { JSXParser } from './jsx-parser';
import { TokenMatcher } from './token-matcher';
import { 
  TokensFile, 
  LintViolation, 
  LintOptions, 
  TokenCandidate,
  ColorToken,
  SpacingToken,
  TypographyToken,
  BorderRadiusToken,
  ComponentDefinition
} from './types';

export class DesignLinter {
  private tokensFile: TokensFile;
  private options: LintOptions;
  private cssParser: CSSParser;
  private jsxParser: JSXParser;

  constructor(tokensFilePath?: string, options: LintOptions = {}) {
    this.options = {
      enableTokenLinting: true,
      enableComponentLinting: true,
      colorThreshold: 10,
      spacingThreshold: 2,
      ...options,
    };

    this.tokensFile = this.loadTokensFile(tokensFilePath);
    this.cssParser = new CSSParser(this.tokensFile, this.options);
    this.jsxParser = new JSXParser(this.tokensFile, this.options);
  }

  /**
   * Lint CSS/SCSS content
   */
  public lintCss(content: string, filePath: string): LintViolation[] {
    if (!this.options.enableTokenLinting) return [];
    return this.cssParser.parseCSSContent(content, filePath);
  }

  /**
   * Lint JSX/TSX content for inline styles
   */
  public lintJsx(content: string, filePath: string): LintViolation[] {
    if (!this.options.enableTokenLinting) return [];
    return this.jsxParser.parseJSXContent(content, filePath);
  }

  /**
   * Lint JSX/TSX content for component violations
   */
  public lintComponents(content: string, filePath: string): LintViolation[] {
    if (!this.options.enableComponentLinting) return [];
    return this.jsxParser.parseJSXContent(content, filePath);
  }

  /**
   * Scan workspace for token candidates
   */
  public async scanWorkspaceForTokens(workspacePath: string): Promise<{
    colors: TokenCandidate[];
    spacing: TokenCandidate[];
    typography: TokenCandidate[];
    borderRadius: TokenCandidate[];
  }> {
    const candidates = {
      colors: [] as TokenCandidate[],
      spacing: [] as TokenCandidate[],
      typography: [] as TokenCandidate[],
      borderRadius: [] as TokenCandidate[],
    };

    const files = await this.findFiles(workspacePath, [
      '**/*.css',
      '**/*.scss',
      '**/*.jsx',
      '**/*.tsx',
    ]);

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf-8');
      const fileCandidates = this.extractTokenCandidates(content, file);
      
      candidates.colors.push(...fileCandidates.colors);
      candidates.spacing.push(...fileCandidates.spacing);
      candidates.typography.push(...fileCandidates.typography);
      candidates.borderRadius.push(...fileCandidates.borderRadius);
    }

    // Cluster similar values
    candidates.colors = TokenMatcher.clusterColors(candidates.colors, this.options.colorThreshold);
    candidates.spacing = TokenMatcher.clusterSpacing(candidates.spacing, this.options.spacingThreshold);
    candidates.typography = TokenMatcher.clusterTypography(candidates.typography);
    candidates.borderRadius = TokenMatcher.clusterBorderRadius(candidates.borderRadius);

    // Generate suggested names
    candidates.colors = TokenMatcher.generateTokenNames(candidates.colors);
    candidates.spacing = TokenMatcher.generateTokenNames(candidates.spacing);
    candidates.typography = TokenMatcher.generateTokenNames(candidates.typography);
    candidates.borderRadius = TokenMatcher.generateTokenNames(candidates.borderRadius);

    return candidates;
  }

  /**
   * Scan workspace for React components
   */
  public async scanWorkspaceForComponents(workspacePath: string, componentDir: string = 'src/components/ui'): Promise<ComponentDefinition[]> {
    const components: ComponentDefinition[] = [];
    const componentPath = path.join(workspacePath, componentDir);

    if (!fs.existsSync(componentPath)) {
      return components;
    }

    const files = await this.findFiles(componentPath, ['**/*.tsx', '**/*.jsx']);

    for (const file of files) {
      const content = fs.readFileSync(file, 'utf-8');
      const componentDef = this.extractComponentDefinition(content, file, workspacePath);
      
      if (componentDef) {
        components.push(componentDef);
      }
    }

    return components;
  }

  /**
   * Create tokens file from candidates
   */
  public createTokensFile(
    candidates: {
      colors: TokenCandidate[];
      spacing: TokenCandidate[];
      typography: TokenCandidate[];
      borderRadius: TokenCandidate[];
    },
    components: ComponentDefinition[] = []
  ): TokensFile {
    const tokens = TokenMatcher.candidatesToTokens([
      ...candidates.colors,
      ...candidates.spacing,
      ...candidates.typography,
      ...candidates.borderRadius,
    ]);

    const componentsMap: Record<string, ComponentDefinition> = {};
    components.forEach(comp => {
      componentsMap[comp.name] = comp;
    });

    return {
      colors: tokens.colors,
      spacing: tokens.spacing,
      typography: tokens.typography,
      borderRadius: tokens.borderRadius,
      components: componentsMap,
    };
  }

  /**
   * Save tokens file
   */
  public saveTokensFile(tokensFile: TokensFile, filePath: string): void {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, JSON.stringify(tokensFile, null, 2));
  }

  /**
   * Update tokens file with new token
   */
  public addTokenToFile(token: ColorToken | SpacingToken | TypographyToken | BorderRadiusToken, tokensFilePath: string): void {
    const tokensFile = this.loadTokensFile(tokensFilePath);
    
    switch (token.type) {
      case 'color':
        tokensFile.colors.push(token as ColorToken);
        break;
      case 'spacing':
        tokensFile.spacing.push(token as SpacingToken);
        break;
      case 'typography':
        tokensFile.typography.push(token as TypographyToken);
        break;
      case 'borderRadius':
        tokensFile.borderRadius.push(token as BorderRadiusToken);
        break;
    }

    this.saveTokensFile(tokensFile, tokensFilePath);
    this.tokensFile = tokensFile; // Update internal state
  }

  // Private helper methods
  private loadTokensFile(tokensFilePath?: string): TokensFile {
    const defaultTokensFile: TokensFile = {
      colors: [],
      spacing: [],
      typography: [],
      borderRadius: [],
      components: {},
    };

    if (!tokensFilePath || !fs.existsSync(tokensFilePath)) {
      return defaultTokensFile;
    }

    try {
      const content = fs.readFileSync(tokensFilePath, 'utf-8');
      const parsed = JSON.parse(content);
      
      return {
        colors: parsed.colors || [],
        spacing: parsed.spacing || [],
        typography: parsed.typography || [],
        borderRadius: parsed.borderRadius || [],
        components: parsed.components || {},
      };
    } catch (error) {
      console.warn(`Failed to load tokens file ${tokensFilePath}:`, error);
      return defaultTokensFile;
    }
  }

  private async findFiles(dir: string, patterns: string[]): Promise<string[]> {
    const files: string[] = [];
    
    const walk = (currentDir: string) => {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile()) {
          const ext = path.extname(item);
          if (patterns.some(pattern => {
            const regex = new RegExp(pattern.replace('**/*', '.*').replace('*', '[^/]*'));
            return regex.test(fullPath);
          })) {
            files.push(fullPath);
          }
        }
      }
    };

    if (fs.existsSync(dir)) {
      walk(dir);
    }

    return files;
  }

  private extractTokenCandidates(content: string, filePath: string): {
    colors: TokenCandidate[];
    spacing: TokenCandidate[];
    typography: TokenCandidate[];
    borderRadius: TokenCandidate[];
  } {
    const candidates = {
      colors: [] as TokenCandidate[],
      spacing: [] as TokenCandidate[],
      typography: [] as TokenCandidate[],
      borderRadius: [] as TokenCandidate[],
    };

    // Extract from CSS/SCSS
    if (filePath.endsWith('.css') || filePath.endsWith('.scss')) {
      // This is a simplified extraction - in a full implementation,
      // you'd use the CSS parser to extract actual values
      const colorRegex = /#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)/g;
      const spacingRegex = /\b\d+(?:\.\d+)?(px|rem|em)\b/g;
      
      let match;
      while ((match = colorRegex.exec(content)) !== null) {
        candidates.colors.push({
          value: match[0],
          type: 'color',
          occurrences: [{ file: filePath, line: 1, column: match.index }],
        });
      }
      
      while ((match = spacingRegex.exec(content)) !== null) {
        candidates.spacing.push({
          value: match[0],
          type: 'spacing',
          occurrences: [{ file: filePath, line: 1, column: match.index }],
        });
      }
    }

    return candidates;
  }

  private extractComponentDefinition(content: string, filePath: string, workspacePath: string): ComponentDefinition | null {
    // Extract component name from file path
    const fileName = path.basename(filePath, path.extname(filePath));
    
    // Check if it's a valid React component (PascalCase)
    if (!/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
      return null;
    }

    // Generate relative import path
    const relativePath = path.relative(workspacePath, filePath);
    const importPath = './' + relativePath.replace(/\\/g, '/');

    return {
      name: fileName,
      importPath,
      requiredProps: [], // Could be extracted from PropTypes or TypeScript interfaces
      requiredClasses: [], // Could be extracted from className usage analysis
    };
  }
}

// Export types and utilities
export * from './types';
export { TokenMatcher } from './token-matcher';
export { CSSParser } from './css-parser';
export { JSXParser } from './jsx-parser';
