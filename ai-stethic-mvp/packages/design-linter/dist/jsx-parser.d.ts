import { LintViolation, TokensFile, LintOptions } from './types';
export declare class JSXParser {
    private tokensFile;
    private options;
    constructor(tokensFile: TokensFile, options?: LintOptions);
    /**
     * Parse JSX/TSX content and extract violations
     */
    parseJSXContent(content: string, filePath: string): LintViolation[];
    /**
     * Extract import statements from AST
     */
    private extractImports;
    /**
     * Extract JSX elements from AST
     */
    private extractJSXElements;
    /**
     * Extract component definitions from AST
     */
    private extractComponentDefinitions;
    /**
     * Parse individual JSX element
     */
    private parseJSXElement;
    /**
     * Check for inline style violations
     */
    private checkInlineStyleViolations;
    /**
     * Check for component violations
     */
    private checkComponentViolations;
    /**
     * Check for duplicate component definitions
     */
    private checkDuplicateComponents;
    /**
     * Check style property for token violations
     */
    private checkStylePropertyViolation;
    private isPascalCase;
    private isColorProperty;
    private findClosestColorToken;
    private calculateColorDistance;
}
//# sourceMappingURL=jsx-parser.d.ts.map