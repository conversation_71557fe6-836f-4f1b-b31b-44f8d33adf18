import { TokenCandidate, ColorToken, SpacingToken, TypographyToken, BorderRadiusToken } from './types';
export declare class TokenMatcher {
    /**
     * Cluster similar color values
     */
    static clusterColors(candidates: TokenCandidate[], threshold?: number): TokenCandidate[];
    /**
     * Cluster similar spacing values
     */
    static clusterSpacing(candidates: TokenCandidate[], threshold?: number): TokenCandidate[];
    /**
     * Cluster similar border-radius values
     */
    static clusterBorderRadius(candidates: TokenCandidate[]): TokenCandidate[];
    /**
     * Cluster typography values
     */
    static clusterTypography(candidates: TokenCandidate[]): TokenCandidate[];
    /**
     * Generate suggested token names
     */
    static generateTokenNames(candidates: TokenCandidate[]): TokenCandidate[];
    /**
     * Convert candidates to token objects
     */
    static candidatesToTokens(candidates: TokenCandidate[]): {
        colors: ColorToken[];
        spacing: SpacingToken[];
        typography: TypographyToken[];
        borderRadius: BorderRadiusToken[];
    };
    private static calculateColorDistance;
    private static parsePixelValue;
    private static extractUnit;
    private static isFontFamily;
    private static isFontSize;
    private static normalizeFontFamily;
    private static generateColorName;
    private static generateSpacingName;
    private static generateTypographyName;
    private static generateBorderRadiusName;
}
//# sourceMappingURL=token-matcher.d.ts.map