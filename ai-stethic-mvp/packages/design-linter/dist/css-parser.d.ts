import { LintViolation, TokensFile, LintOptions } from './types';
export declare class CSSParser {
    private tokensFile;
    private options;
    constructor(tokensFile: TokensFile, options?: LintOptions);
    /**
     * Parse CSS/SCSS content and extract design token violations
     */
    parseCSSContent(content: string, filePath: string): LintViolation[];
    /**
     * Extract CSS rule information from PostCSS declaration
     */
    private extractCSSRule;
    /**
     * Check if a CSS rule violates design tokens
     */
    private checkTokenViolation;
    /**
     * Check for color token violations
     */
    private checkColorViolation;
    /**
     * Check for spacing token violations
     */
    private checkSpacingViolation;
    /**
     * Check for border-radius token violations
     */
    private checkBorderRadiusViolation;
    /**
     * Check for typography token violations
     */
    private checkTypographyViolation;
    private isColorProperty;
    private isSpacingProperty;
    private isTypographyProperty;
    private extractSpacingValues;
    private parseNumericValue;
    private findClosestColorToken;
    private findClosestSpacingToken;
    private findClosestBorderRadiusToken;
    private calculateColorDistance;
}
//# sourceMappingURL=css-parser.d.ts.map