"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSXParser = exports.CSSParser = exports.TokenMatcher = exports.DesignLinter = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const css_parser_1 = require("./css-parser");
const jsx_parser_1 = require("./jsx-parser");
const token_matcher_1 = require("./token-matcher");
class DesignLinter {
    constructor(tokensFilePath, options = {}) {
        this.options = {
            enableTokenLinting: true,
            enableComponentLinting: true,
            colorThreshold: 10,
            spacingThreshold: 2,
            ...options,
        };
        this.tokensFile = this.loadTokensFile(tokensFilePath);
        this.cssParser = new css_parser_1.CSSParser(this.tokensFile, this.options);
        this.jsxParser = new jsx_parser_1.JSXParser(this.tokensFile, this.options);
    }
    /**
     * Lint CSS/SCSS content
     */
    lintCss(content, filePath) {
        if (!this.options.enableTokenLinting)
            return [];
        return this.cssParser.parseCSSContent(content, filePath);
    }
    /**
     * Lint JSX/TSX content for inline styles
     */
    lintJsx(content, filePath) {
        if (!this.options.enableTokenLinting)
            return [];
        return this.jsxParser.parseJSXContent(content, filePath);
    }
    /**
     * Lint JSX/TSX content for component violations
     */
    lintComponents(content, filePath) {
        if (!this.options.enableComponentLinting)
            return [];
        return this.jsxParser.parseJSXContent(content, filePath);
    }
    /**
     * Scan workspace for token candidates
     */
    async scanWorkspaceForTokens(workspacePath) {
        const candidates = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
        };
        const files = await this.findFiles(workspacePath, [
            '**/*.css',
            '**/*.scss',
            '**/*.jsx',
            '**/*.tsx',
        ]);
        for (const file of files) {
            const content = fs.readFileSync(file, 'utf-8');
            const fileCandidates = this.extractTokenCandidates(content, file);
            candidates.colors.push(...fileCandidates.colors);
            candidates.spacing.push(...fileCandidates.spacing);
            candidates.typography.push(...fileCandidates.typography);
            candidates.borderRadius.push(...fileCandidates.borderRadius);
        }
        // Cluster similar values
        candidates.colors = token_matcher_1.TokenMatcher.clusterColors(candidates.colors, this.options.colorThreshold);
        candidates.spacing = token_matcher_1.TokenMatcher.clusterSpacing(candidates.spacing, this.options.spacingThreshold);
        candidates.typography = token_matcher_1.TokenMatcher.clusterTypography(candidates.typography);
        candidates.borderRadius = token_matcher_1.TokenMatcher.clusterBorderRadius(candidates.borderRadius);
        // Generate suggested names
        candidates.colors = token_matcher_1.TokenMatcher.generateTokenNames(candidates.colors);
        candidates.spacing = token_matcher_1.TokenMatcher.generateTokenNames(candidates.spacing);
        candidates.typography = token_matcher_1.TokenMatcher.generateTokenNames(candidates.typography);
        candidates.borderRadius = token_matcher_1.TokenMatcher.generateTokenNames(candidates.borderRadius);
        return candidates;
    }
    /**
     * Scan workspace for React components
     */
    async scanWorkspaceForComponents(workspacePath, componentDir = 'src/components/ui') {
        const components = [];
        const componentPath = path.join(workspacePath, componentDir);
        if (!fs.existsSync(componentPath)) {
            return components;
        }
        const files = await this.findFiles(componentPath, ['**/*.tsx', '**/*.jsx']);
        for (const file of files) {
            const content = fs.readFileSync(file, 'utf-8');
            const componentDef = this.extractComponentDefinition(content, file, workspacePath);
            if (componentDef) {
                components.push(componentDef);
            }
        }
        return components;
    }
    /**
     * Create tokens file from candidates
     */
    createTokensFile(candidates, components = []) {
        const tokens = token_matcher_1.TokenMatcher.candidatesToTokens([
            ...candidates.colors,
            ...candidates.spacing,
            ...candidates.typography,
            ...candidates.borderRadius,
        ]);
        const componentsMap = {};
        components.forEach(comp => {
            componentsMap[comp.name] = comp;
        });
        return {
            colors: tokens.colors,
            spacing: tokens.spacing,
            typography: tokens.typography,
            borderRadius: tokens.borderRadius,
            components: componentsMap,
        };
    }
    /**
     * Save tokens file
     */
    saveTokensFile(tokensFile, filePath) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, JSON.stringify(tokensFile, null, 2));
    }
    /**
     * Update tokens file with new token
     */
    addTokenToFile(token, tokensFilePath) {
        const tokensFile = this.loadTokensFile(tokensFilePath);
        switch (token.type) {
            case 'color':
                tokensFile.colors.push(token);
                break;
            case 'spacing':
                tokensFile.spacing.push(token);
                break;
            case 'typography':
                tokensFile.typography.push(token);
                break;
            case 'borderRadius':
                tokensFile.borderRadius.push(token);
                break;
        }
        this.saveTokensFile(tokensFile, tokensFilePath);
        this.tokensFile = tokensFile; // Update internal state
    }
    // Private helper methods
    loadTokensFile(tokensFilePath) {
        const defaultTokensFile = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
            components: {},
        };
        if (!tokensFilePath || !fs.existsSync(tokensFilePath)) {
            return defaultTokensFile;
        }
        try {
            const content = fs.readFileSync(tokensFilePath, 'utf-8');
            const parsed = JSON.parse(content);
            return {
                colors: parsed.colors || [],
                spacing: parsed.spacing || [],
                typography: parsed.typography || [],
                borderRadius: parsed.borderRadius || [],
                components: parsed.components || {},
            };
        }
        catch (error) {
            console.warn(`Failed to load tokens file ${tokensFilePath}:`, error);
            return defaultTokensFile;
        }
    }
    async findFiles(dir, patterns) {
        const files = [];
        const walk = (currentDir) => {
            const items = fs.readdirSync(currentDir);
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                    walk(fullPath);
                }
                else if (stat.isFile()) {
                    const ext = path.extname(item);
                    if (patterns.some(pattern => {
                        const regex = new RegExp(pattern.replace('**/*', '.*').replace('*', '[^/]*'));
                        return regex.test(fullPath);
                    })) {
                        files.push(fullPath);
                    }
                }
            }
        };
        if (fs.existsSync(dir)) {
            walk(dir);
        }
        return files;
    }
    extractTokenCandidates(content, filePath) {
        const candidates = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
        };
        // Extract from CSS/SCSS
        if (filePath.endsWith('.css') || filePath.endsWith('.scss')) {
            // This is a simplified extraction - in a full implementation,
            // you'd use the CSS parser to extract actual values
            const colorRegex = /#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)/g;
            const spacingRegex = /\b\d+(?:\.\d+)?(px|rem|em)\b/g;
            let match;
            while ((match = colorRegex.exec(content)) !== null) {
                candidates.colors.push({
                    value: match[0],
                    type: 'color',
                    occurrences: [{ file: filePath, line: 1, column: match.index }],
                });
            }
            while ((match = spacingRegex.exec(content)) !== null) {
                candidates.spacing.push({
                    value: match[0],
                    type: 'spacing',
                    occurrences: [{ file: filePath, line: 1, column: match.index }],
                });
            }
        }
        return candidates;
    }
    extractComponentDefinition(content, filePath, workspacePath) {
        // Extract component name from file path
        const fileName = path.basename(filePath, path.extname(filePath));
        // Check if it's a valid React component (PascalCase)
        if (!/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
            return null;
        }
        // Generate relative import path
        const relativePath = path.relative(workspacePath, filePath);
        const importPath = './' + relativePath.replace(/\\/g, '/');
        return {
            name: fileName,
            importPath,
            requiredProps: [], // Could be extracted from PropTypes or TypeScript interfaces
            requiredClasses: [], // Could be extracted from className usage analysis
        };
    }
}
exports.DesignLinter = DesignLinter;
// Export types and utilities
__exportStar(require("./types"), exports);
var token_matcher_2 = require("./token-matcher");
Object.defineProperty(exports, "TokenMatcher", { enumerable: true, get: function () { return token_matcher_2.TokenMatcher; } });
var css_parser_2 = require("./css-parser");
Object.defineProperty(exports, "CSSParser", { enumerable: true, get: function () { return css_parser_2.CSSParser; } });
var jsx_parser_2 = require("./jsx-parser");
Object.defineProperty(exports, "JSXParser", { enumerable: true, get: function () { return jsx_parser_2.JSXParser; } });
//# sourceMappingURL=index.js.map