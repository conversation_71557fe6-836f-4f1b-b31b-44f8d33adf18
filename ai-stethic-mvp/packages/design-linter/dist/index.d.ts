import { TokensFile, LintViolation, LintOptions, TokenCandidate, ColorToken, SpacingToken, TypographyToken, BorderRadiusToken, ComponentDefinition } from './types';
export declare class DesignLinter {
    private tokensFile;
    private options;
    private cssParser;
    private jsxParser;
    constructor(tokensFilePath?: string, options?: LintOptions);
    /**
     * Lint CSS/SCSS content
     */
    lintCss(content: string, filePath: string): LintViolation[];
    /**
     * Lint JSX/TSX content for inline styles
     */
    lintJsx(content: string, filePath: string): LintViolation[];
    /**
     * Lint JSX/TSX content for component violations
     */
    lintComponents(content: string, filePath: string): LintViolation[];
    /**
     * Scan workspace for token candidates
     */
    scanWorkspaceForTokens(workspacePath: string): Promise<{
        colors: TokenCandidate[];
        spacing: TokenCandidate[];
        typography: TokenCandidate[];
        borderRadius: TokenCandidate[];
    }>;
    /**
     * Scan workspace for React components
     */
    scanWorkspaceForComponents(workspacePath: string, componentDir?: string): Promise<ComponentDefinition[]>;
    /**
     * Create tokens file from candidates
     */
    createTokensFile(candidates: {
        colors: TokenCandidate[];
        spacing: TokenCandidate[];
        typography: TokenCandidate[];
        borderRadius: TokenCandidate[];
    }, components?: ComponentDefinition[]): TokensFile;
    /**
     * Save tokens file
     */
    saveTokensFile(tokensFile: TokensFile, filePath: string): void;
    /**
     * Update tokens file with new token
     */
    addTokenToFile(token: ColorToken | SpacingToken | TypographyToken | BorderRadiusToken, tokensFilePath: string): void;
    private loadTokensFile;
    private findFiles;
    private extractTokenCandidates;
    private extractComponentDefinition;
}
export * from './types';
export { TokenMatcher } from './token-matcher';
export { CSSParser } from './css-parser';
export { JSXParser } from './jsx-parser';
//# sourceMappingURL=index.d.ts.map