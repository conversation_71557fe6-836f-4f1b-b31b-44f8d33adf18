"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTests = runTests;
const index_1 = require("./index");
// Test tokens file
const testTokensFile = {
    colors: [
        {
            name: 'primary',
            value: '#3b82f6',
            type: 'color',
            hex: '#3b82f6',
            rgb: { r: 59, g: 130, b: 246 },
            hsl: { h: 217, s: 0.91, l: 0.6 },
        },
        {
            name: 'secondary',
            value: '#64748b',
            type: 'color',
            hex: '#64748b',
            rgb: { r: 100, g: 116, b: 139 },
            hsl: { h: 215, s: 0.16, l: 0.47 },
        },
    ],
    spacing: [
        {
            name: 'sm',
            value: '8px',
            type: 'spacing',
            pixels: 8,
            unit: 'px',
        },
        {
            name: 'md',
            value: '16px',
            type: 'spacing',
            pixels: 16,
            unit: 'px',
        },
    ],
    typography: [
        {
            name: 'heading',
            value: 'Inter',
            type: 'typography',
            fontFamily: 'Inter',
        },
        {
            name: 'body',
            value: '16px',
            type: 'typography',
            fontSize: '16px',
        },
    ],
    borderRadius: [
        {
            name: 'rounded',
            value: '4px',
            type: 'borderRadius',
            pixels: 4,
            unit: 'px',
        },
    ],
    components: {
        Button: {
            name: 'Button',
            importPath: './src/components/ui/Button.tsx',
            requiredProps: ['variant'],
            requiredClasses: ['btn'],
        },
    },
};
function runTests() {
    console.log('🧪 Running AI-Stethic Design Linter Tests...\n');
    // Test 1: CSS Linting
    console.log('Test 1: CSS Token Violations');
    const linter = new index_1.DesignLinter();
    linter['tokensFile'] = testTokensFile; // Set test tokens
    linter['cssParser'] = new (require('./css-parser').CSSParser)(testTokensFile);
    const cssContent = `
    .button {
      background-color: #ff0000; /* Should be flagged */
      padding: 12px; /* Should be flagged */
      border-radius: 8px; /* Should be flagged */
      color: #3b82f6; /* Should be valid */
    }
  `;
    const cssViolations = linter.lintCss(cssContent, 'test.css');
    console.log(`Found ${cssViolations.length} CSS violations:`);
    cssViolations.forEach(v => console.log(`  - ${v.message}`));
    console.log('');
    // Test 2: JSX Component Violations
    console.log('Test 2: JSX Component Violations');
    const jsxContent = `
    import React from 'react';
    import Button from './wrong/path/Button';

    function MyComponent() {
      return (
        <div>
          <Button>Click me</Button>
          <Button variant="primary" className="btn">Valid Button</Button>
        </div>
      );
    }
  `;
    const jsxViolations = linter.lintComponents(jsxContent, 'test.tsx');
    console.log(`Found ${jsxViolations.length} JSX violations:`);
    jsxViolations.forEach(v => console.log(`  - ${v.message}`));
    console.log('');
    // Test 3: Token Scanning
    console.log('Test 3: Token Candidate Scanning');
    const scanContent = `
    .card {
      background: #f8fafc;
      padding: 24px;
      border-radius: 12px;
      color: #1e293b;
    }
  `;
    const candidates = linter['extractTokenCandidates'](scanContent, 'scan.css');
    console.log(`Found token candidates:`);
    console.log(`  Colors: ${candidates.colors.length}`);
    console.log(`  Spacing: ${candidates.spacing.length}`);
    console.log('');
    // Test 4: Token File Creation
    console.log('Test 4: Token File Creation');
    const newTokensFile = linter.createTokensFile(candidates, [
        {
            name: 'Card',
            importPath: './src/components/ui/Card.tsx',
            requiredProps: [],
            requiredClasses: [],
        },
    ]);
    console.log(`Created tokens file with:`);
    console.log(`  Colors: ${newTokensFile.colors.length}`);
    console.log(`  Spacing: ${newTokensFile.spacing.length}`);
    console.log(`  Components: ${Object.keys(newTokensFile.components).length}`);
    console.log('');
    console.log('✅ All tests completed!');
}
// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}
//# sourceMappingURL=test.js.map