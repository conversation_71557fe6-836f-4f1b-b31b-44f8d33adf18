{"version": 3, "file": "violations-provider.js", "sourceRoot": "", "sources": ["../../src/views/violations-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,MAAa,kBAAkB;IAM3B;QALQ,yBAAoB,GAAiE,IAAI,MAAM,CAAC,YAAY,EAA2C,CAAC;QACvJ,wBAAmB,GAA0D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAE9G,eAAU,GAAqC,IAAI,GAAG,EAAE,CAAC;IAElD,CAAC;IAEhB,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,gBAAgB,CAAC,GAAe,EAAE,WAAgC;QAC9D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,WAAW,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAsB;QAC9B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAuB;QAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,iBAAiB;QACrB,MAAM,KAAK,GAAoB,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC7B,KAAK,CAAC,IAAI,CAAC,IAAI,aAAa,CACxB,uBAAuB,EACvB,sCAAsC,EACtC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,eAAe,CAClB,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,KAAK,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACrD,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS,CAAC;YAExD,eAAe,IAAI,WAAW,CAAC,MAAM,CAAC;YAEtC,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,IAAI,aAAa,CAC9B,MAAM,QAAQ,KAAK,WAAW,CAAC,MAAM,GAAG,EACxC,GAAG,WAAW,CAAC,MAAM,oBAAoB,QAAQ,EAAE,EACnD,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EACxC,iBAAiB,CACpB,CAAC;YACF,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAErB,4BAA4B;YAC5B,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gBACtC,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;gBAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;gBAChE,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAElD,MAAM,aAAa,GAAG,IAAI,aAAa,CACnC,GAAG,IAAI,SAAS,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,EAClE,UAAU,CAAC,OAAO,EAClB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,WAAW,EACX;oBACI,OAAO,EAAE,aAAa;oBACtB,KAAK,EAAE,WAAW;oBAClB,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC;iBACpD,CACJ,CAAC;gBAEF,aAAa,CAAC,GAAG,GAAG,GAAG,CAAC;gBACxB,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;gBACtC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;QAED,yBAAyB;QACzB,MAAM,WAAW,GAAG,IAAI,aAAa,CACjC,MAAM,eAAe,mBAAmB,EACxC,SAAS,eAAe,oCAAoC,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,EACxF,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,SAAS,CACZ,CAAC;QACF,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAE3B,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,gBAAgB,CAAC,OAAe;QACpC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,OAAO,OAAO,CAAC;QAC9C,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;YAAE,OAAO,SAAS,CAAC;QAClD,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;YAAE,OAAO,YAAY,CAAC;QACxD,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,OAAO,QAAQ,CAAC;QAChD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,gBAAgB,CAAC,IAAY;QACjC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;YAC1B,KAAK,SAAS,CAAC,CAAC,OAAO,IAAI,CAAC;YAC5B,KAAK,YAAY,CAAC,CAAC,OAAO,IAAI,CAAC;YAC/B,KAAK,QAAQ,CAAC,CAAC,OAAO,IAAI,CAAC;YAC3B,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC;QACzB,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,OAAe;QAClC,sCAAsC;QACtC,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;QAC5C,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,kBAAkB;QACd,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,KAAK,IAAI,WAAW,CAAC,MAAM,CAAC;QAChC,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,mBAAmB;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;CACJ;AAtID,gDAsIC;AAED,MAAa,aAAc,SAAQ,MAAM,CAAC,QAAQ;IAI9C,YACoB,KAAa,EACb,OAAe,EACf,gBAAiD,EACjD,YAAoB,EACpB,OAAwB;QAExC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QANf,UAAK,GAAL,KAAK,CAAQ;QACb,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,iBAAY,GAAZ,YAAY,CAAQ;QACpB,YAAO,GAAP,OAAO,CAAiB;QAGxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;CACJ;AAhBD,sCAgBC"}