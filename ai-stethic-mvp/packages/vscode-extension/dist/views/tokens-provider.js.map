{"version": 3, "file": "tokens-provider.js", "sourceRoot": "", "sources": ["../../src/views/tokens-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,MAAa,cAAc;IAIvB;QAHQ,yBAAoB,GAA6D,IAAI,MAAM,CAAC,YAAY,EAAuC,CAAC;QAC/I,wBAAmB,GAAsD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEnG,CAAC;IAEhB,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAkB;QAC1B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAmB;QAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE,CAAC;YAC7C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAS,CAAC,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,kBAAkB;QACtB,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO;gBACH,IAAI,SAAS,CACT,mBAAmB,EACnB,iCAAiC,EACjC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,cAAc,CACjB;aACJ,CAAC;QACN,CAAC;QAED,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAE9E,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YACjC,OAAO;gBACH,IAAI,SAAS,CACT,gBAAgB,EAChB,yCAAyC,EACzC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,WAAW,EACX,SAAS,EACT;oBACI,OAAO,EAAE,4BAA4B;oBACrC,KAAK,EAAE,mBAAmB;iBAC7B,CACJ;aACJ,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAEzC,MAAM,UAAU,GAAgB,EAAE,CAAC;YAEnC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,cAAc,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,EACrC,qBAAqB,EACrB,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,UAAU,EACV,QAAQ,CACX,CAAC,CAAC;YACP,CAAC;YAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,eAAe,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,EACvC,uBAAuB,EACvB,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,UAAU,EACV,SAAS,CACZ,CAAC,CAAC;YACP,CAAC;YAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,kBAAkB,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,EAC7C,0BAA0B,EAC1B,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,UAAU,EACV,YAAY,CACf,CAAC,CAAC;YACP,CAAC;YAED,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,qBAAqB,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,EAClD,6BAA6B,EAC7B,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,UAAU,EACV,cAAc,CACjB,CAAC,CAAC;YACP,CAAC;YAED,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjE,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,kBAAkB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,EAC1D,uBAAuB,EACvB,MAAM,CAAC,wBAAwB,CAAC,SAAS,EACzC,UAAU,EACV,YAAY,CACf,CAAC,CAAC;YACP,CAAC;YAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CACzB,mBAAmB,EACnB,uCAAuC,EACvC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,OAAO,CACV,CAAC,CAAC;YACP,CAAC;YAED,OAAO,UAAU,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,IAAI,SAAS,CACT,qBAAqB,EACrB,mCAAmC,EACnC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,OAAO,CACV;aACJ,CAAC;QACN,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QACzC,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa;YAAE,OAAO,EAAE,CAAC;QAE9B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAE9E,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACzC,MAAM,KAAK,GAAgB,EAAE,CAAC;YAE9B,QAAQ,QAAQ,EAAE,CAAC;gBACf,KAAK,QAAQ;oBACT,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;wBAClC,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,CACpB,GAAG,KAAK,CAAC,IAAI,EAAE,EACf,GAAG,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,KAAK,EAAE,EAC/B,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,EACb,SAAS,EACT,SAAS,EACT,KAAK,CACR,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBAEV,KAAK,SAAS;oBACV,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;wBACnC,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,CACpB,GAAG,KAAK,CAAC,IAAI,EAAE,EACf,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,EACpC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,eAAe,EACf,SAAS,EACT,SAAS,EACT,KAAK,CACR,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBAEV,KAAK,YAAY;oBACb,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;wBACtC,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC;wBACtE,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,CACpB,GAAG,KAAK,CAAC,IAAI,EAAE,EACf,WAAW,EACX,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,kBAAkB,EAClB,SAAS,EACT,SAAS,EACT,KAAK,CACR,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBAEV,KAAK,cAAc;oBACf,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;wBACxC,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,CACpB,GAAG,KAAK,CAAC,IAAI,EAAE,EACf,GAAG,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,EACpC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,qBAAqB,EACrB,SAAS,EACT,SAAS,EACT,KAAK,CACR,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;gBAEV,KAAK,YAAY;oBACb,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAgB,EAAE,EAAE;wBACjF,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa,EAAE,MAAM,IAAI,CAAC,CAAC;wBAC3D,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe,EAAE,MAAM,IAAI,CAAC,CAAC;wBAC/D,KAAK,CAAC,IAAI,CAAC,IAAI,SAAS,CACpB,GAAG,IAAI,EAAE,EACT,GAAG,SAAS,CAAC,UAAU,KAAK,aAAa,WAAW,eAAe,WAAW,EAC9E,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,iBAAiB,EACjB,SAAS,EACT,SAAS,EACT,SAAS,CACZ,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC;oBACH,MAAM;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,IAAI,SAAS,CACT,sBAAsB,EACtB,6BAA6B,EAC7B,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,OAAO,CACV;aACJ,CAAC;QACN,CAAC;IACL,CAAC;CACJ;AA1OD,wCA0OC;AAED,MAAa,SAAU,SAAQ,MAAM,CAAC,QAAQ;IAC1C,YACoB,KAAa,EACb,OAAe,EACf,gBAAiD,EACjD,YAAoB,EACpB,QAAiB,EACjB,OAAwB,EACxB,SAAe;QAE/B,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QARf,UAAK,GAAL,KAAK,CAAQ;QACb,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,iBAAY,GAAZ,YAAY,CAAQ;QACpB,aAAQ,GAAR,QAAQ,CAAS;QACjB,YAAO,GAAP,OAAO,CAAiB;QACxB,cAAS,GAAT,SAAS,CAAM;QAG/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,qCAAqC;QACrC,IAAI,YAAY,KAAK,aAAa,IAAI,SAAS,EAAE,GAAG,EAAE,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;QAC/F,CAAC;IACL,CAAC;CACJ;AApBD,8BAoBC"}