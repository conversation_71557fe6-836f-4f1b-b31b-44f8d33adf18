"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ViolationItem = exports.ViolationsProvider = void 0;
const vscode = __importStar(require("vscode"));
class ViolationsProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.violations = new Map();
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    updateViolations(uri, diagnostics) {
        if (diagnostics.length === 0) {
            this.violations.delete(uri.toString());
        }
        else {
            this.violations.set(uri.toString(), diagnostics);
        }
        this.refresh();
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(this.getViolationItems());
        }
        return Promise.resolve([]);
    }
    getViolationItems() {
        const items = [];
        if (this.violations.size === 0) {
            items.push(new ViolationItem('✅ No Violations Found', 'Your code follows the design system!', vscode.TreeItemCollapsibleState.None, 'no-violations'));
            return items;
        }
        let totalViolations = 0;
        for (const [uriString, diagnostics] of this.violations) {
            const uri = vscode.Uri.parse(uriString);
            const fileName = uri.path.split('/').pop() || 'Unknown';
            totalViolations += diagnostics.length;
            // Group violations by file
            const fileItem = new ViolationItem(`📄 ${fileName} (${diagnostics.length})`, `${diagnostics.length} violation(s) in ${fileName}`, vscode.TreeItemCollapsibleState.Expanded, 'file-violations');
            items.push(fileItem);
            // Add individual violations
            diagnostics.forEach((diagnostic, index) => {
                const line = diagnostic.range.start.line + 1;
                const violationType = this.getViolationType(diagnostic.message);
                const icon = this.getViolationIcon(violationType);
                const violationItem = new ViolationItem(`${icon} Line ${line}: ${this.shortenMessage(diagnostic.message)}`, diagnostic.message, vscode.TreeItemCollapsibleState.None, 'violation', {
                    command: 'vscode.open',
                    title: 'Open File',
                    arguments: [uri, { selection: diagnostic.range }]
                });
                violationItem.uri = uri;
                violationItem.diagnostic = diagnostic;
                items.push(violationItem);
            });
        }
        // Add summary at the top
        const summaryItem = new ViolationItem(`⚠️ ${totalViolations} Total Violations`, `Found ${totalViolations} design system violations across ${this.violations.size} files`, vscode.TreeItemCollapsibleState.None, 'summary');
        items.unshift(summaryItem);
        return items;
    }
    getViolationType(message) {
        if (message.includes('Color'))
            return 'color';
        if (message.includes('Spacing'))
            return 'spacing';
        if (message.includes('Typography'))
            return 'typography';
        if (message.includes('Border'))
            return 'border';
        return 'unknown';
    }
    getViolationIcon(type) {
        switch (type) {
            case 'color': return '🎨';
            case 'spacing': return '📏';
            case 'typography': return '🔤';
            case 'border': return '🔲';
            default: return '⚠️';
        }
    }
    shortenMessage(message) {
        // Shorten long messages for tree view
        if (message.length > 50) {
            return message.substring(0, 47) + '...';
        }
        return message;
    }
    getTotalViolations() {
        let total = 0;
        for (const diagnostics of this.violations.values()) {
            total += diagnostics.length;
        }
        return total;
    }
    getViolationsByFile() {
        return this.violations;
    }
}
exports.ViolationsProvider = ViolationsProvider;
class ViolationItem extends vscode.TreeItem {
    constructor(label, tooltip, collapsibleState, contextValue, command) {
        super(label, collapsibleState);
        this.label = label;
        this.tooltip = tooltip;
        this.collapsibleState = collapsibleState;
        this.contextValue = contextValue;
        this.command = command;
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
    }
}
exports.ViolationItem = ViolationItem;
//# sourceMappingURL=violations-provider.js.map