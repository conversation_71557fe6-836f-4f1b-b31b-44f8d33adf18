{"version": 3, "file": "overview-provider.js", "sourceRoot": "", "sources": ["../../src/views/overview-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,MAAa,gBAAgB;IAIzB;QAHQ,yBAAoB,GAAgE,IAAI,MAAM,CAAC,YAAY,EAA0C,CAAC;QACrJ,wBAAmB,GAAyD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAEtG,CAAC;IAEhB,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAqB;QAC7B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAsB;QAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,gBAAgB;QACpB,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,OAAO;gBACH,IAAI,YAAY,CACZ,mBAAmB,EACnB,oCAAoC,EACpC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,SAAS,CACZ;aACJ,CAAC;QACN,CAAC;QAED,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC9E,MAAM,SAAS,GAAG,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QAEhD,MAAM,KAAK,GAAmB,EAAE,CAAC;QAEjC,cAAc;QACd,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,MAAM,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBAEzC,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,qBAAqB,EACrB,sCAAsC,EACtC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,eAAe,CAClB,CAAC,CAAC;gBAEH,eAAe;gBACf,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,MAAM,MAAM,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,SAAS,EACzC,qBAAqB,EACrB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,CAChB,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,MAAM,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,EAC3C,6BAA6B,EAC7B,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,CAChB,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,MAAM,MAAM,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,aAAa,EACjD,yBAAyB,EACzB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,CAChB,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,MAAM,MAAM,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,gBAAgB,EACtD,4BAA4B,EAC5B,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,CAChB,CAAC,CAAC;gBAEH,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,MAAM,aAAa,EAC9D,uBAAuB,EACvB,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,aAAa,CAChB,CAAC,CAAC;YAEP,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,uBAAuB,EACvB,mCAAmC,EACnC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,OAAO,CACV,CAAC,CAAC;YACP,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,oBAAoB,EACpB,mCAAmC,EACnC,MAAM,CAAC,wBAAwB,CAAC,IAAI,EACpC,SAAS,EACT;gBACI,OAAO,EAAE,4BAA4B;gBACrC,KAAK,EAAE,mBAAmB;aAC7B,CACJ,CAAC,CAAC;QACP,CAAC;QAED,gBAAgB;QAChB,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,CACvB,kBAAkB,EAClB,EAAE,EACF,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EACxC,SAAS,CACZ,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AAxHD,4CAwHC;AAED,MAAa,YAAa,SAAQ,MAAM,CAAC,QAAQ;IAC7C,YACoB,KAAa,EACb,OAAe,EACf,gBAAiD,EACjD,YAAoB,EACpB,OAAwB;QAExC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QANf,UAAK,GAAL,KAAK,CAAQ;QACb,YAAO,GAAP,OAAO,CAAQ;QACf,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,iBAAY,GAAZ,YAAY,CAAQ;QACpB,YAAO,GAAP,OAAO,CAAiB;QAGxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;CACJ;AAbD,oCAaC"}