"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenItem = exports.TokensProvider = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class TokensProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(this.getTokenCategories());
        }
        else if (element.contextValue === 'category') {
            return Promise.resolve(this.getTokensForCategory(element.category));
        }
        return Promise.resolve([]);
    }
    getTokenCategories() {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return [
                new TokenItem('No Workspace Open', 'Open a workspace to view tokens', vscode.TreeItemCollapsibleState.None, 'no-workspace')
            ];
        }
        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        if (!fs.existsSync(tokensFilePath)) {
            return [
                new TokenItem('No Tokens File', 'Initialize design tokens to get started', vscode.TreeItemCollapsibleState.None, 'no-tokens', undefined, {
                    command: 'aiStethic.initializeTokens',
                    title: 'Initialize Tokens'
                })
            ];
        }
        try {
            const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
            const tokens = JSON.parse(tokensContent);
            const categories = [];
            if (tokens.colors && tokens.colors.length > 0) {
                categories.push(new TokenItem(`🎨 Colors (${tokens.colors.length})`, 'Color design tokens', vscode.TreeItemCollapsibleState.Collapsed, 'category', 'colors'));
            }
            if (tokens.spacing && tokens.spacing.length > 0) {
                categories.push(new TokenItem(`📏 Spacing (${tokens.spacing.length})`, 'Spacing design tokens', vscode.TreeItemCollapsibleState.Collapsed, 'category', 'spacing'));
            }
            if (tokens.typography && tokens.typography.length > 0) {
                categories.push(new TokenItem(`🔤 Typography (${tokens.typography.length})`, 'Typography design tokens', vscode.TreeItemCollapsibleState.Collapsed, 'category', 'typography'));
            }
            if (tokens.borderRadius && tokens.borderRadius.length > 0) {
                categories.push(new TokenItem(`🔲 Border Radius (${tokens.borderRadius.length})`, 'Border radius design tokens', vscode.TreeItemCollapsibleState.Collapsed, 'category', 'borderRadius'));
            }
            if (tokens.components && Object.keys(tokens.components).length > 0) {
                categories.push(new TokenItem(`⚛️ Components (${Object.keys(tokens.components).length})`, 'Registered components', vscode.TreeItemCollapsibleState.Collapsed, 'category', 'components'));
            }
            if (categories.length === 0) {
                categories.push(new TokenItem('No Tokens Defined', 'Add some design tokens to get started', vscode.TreeItemCollapsibleState.None, 'empty'));
            }
            return categories;
        }
        catch (error) {
            return [
                new TokenItem('Invalid Tokens File', 'tokens.json contains invalid JSON', vscode.TreeItemCollapsibleState.None, 'error')
            ];
        }
    }
    getTokensForCategory(category) {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot)
            return [];
        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        try {
            const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
            const tokens = JSON.parse(tokensContent);
            const items = [];
            switch (category) {
                case 'colors':
                    tokens.colors?.forEach((token) => {
                        items.push(new TokenItem(`${token.name}`, `${token.hex} - ${token.value}`, vscode.TreeItemCollapsibleState.None, 'color-token', undefined, undefined, token));
                    });
                    break;
                case 'spacing':
                    tokens.spacing?.forEach((token) => {
                        items.push(new TokenItem(`${token.name}`, `${token.value} (${token.pixels}px)`, vscode.TreeItemCollapsibleState.None, 'spacing-token', undefined, undefined, token));
                    });
                    break;
                case 'typography':
                    tokens.typography?.forEach((token) => {
                        const description = token.fontFamily || token.fontSize || token.value;
                        items.push(new TokenItem(`${token.name}`, description, vscode.TreeItemCollapsibleState.None, 'typography-token', undefined, undefined, token));
                    });
                    break;
                case 'borderRadius':
                    tokens.borderRadius?.forEach((token) => {
                        items.push(new TokenItem(`${token.name}`, `${token.value} (${token.pixels}px)`, vscode.TreeItemCollapsibleState.None, 'border-radius-token', undefined, undefined, token));
                    });
                    break;
                case 'components':
                    Object.entries(tokens.components || {}).forEach(([name, component]) => {
                        const requiredProps = component.requiredProps?.length || 0;
                        const requiredClasses = component.requiredClasses?.length || 0;
                        items.push(new TokenItem(`${name}`, `${component.importPath} (${requiredProps} props, ${requiredClasses} classes)`, vscode.TreeItemCollapsibleState.None, 'component-token', undefined, undefined, component));
                    });
                    break;
            }
            return items;
        }
        catch (error) {
            return [
                new TokenItem('Error Loading Tokens', 'Failed to parse tokens file', vscode.TreeItemCollapsibleState.None, 'error')
            ];
        }
    }
}
exports.TokensProvider = TokensProvider;
class TokenItem extends vscode.TreeItem {
    constructor(label, tooltip, collapsibleState, contextValue, category, command, tokenData) {
        super(label, collapsibleState);
        this.label = label;
        this.tooltip = tooltip;
        this.collapsibleState = collapsibleState;
        this.contextValue = contextValue;
        this.category = category;
        this.command = command;
        this.tokenData = tokenData;
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
        // Add color preview for color tokens
        if (contextValue === 'color-token' && tokenData?.hex) {
            this.iconPath = new vscode.ThemeIcon('circle-filled', new vscode.ThemeColor('charts.red'));
        }
    }
}
exports.TokenItem = TokenItem;
//# sourceMappingURL=tokens-provider.js.map