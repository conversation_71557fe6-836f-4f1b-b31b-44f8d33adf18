"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.OverviewItem = exports.OverviewProvider = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class OverviewProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
    }
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve(this.getOverviewItems());
        }
        return Promise.resolve([]);
    }
    getOverviewItems() {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return [
                new OverviewItem('No Workspace Open', 'Open a workspace to use AI-Stethic', vscode.TreeItemCollapsibleState.None, 'warning')
            ];
        }
        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        const hasTokens = fs.existsSync(tokensFilePath);
        const items = [];
        // Status item
        if (hasTokens) {
            try {
                const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
                const tokens = JSON.parse(tokensContent);
                items.push(new OverviewItem('✅ AI-Stethic Active', 'Design system enforcement is running', vscode.TreeItemCollapsibleState.None, 'status-active'));
                // Token counts
                items.push(new OverviewItem(`🎨 ${tokens.colors?.length || 0} Colors`, 'Design token colors', vscode.TreeItemCollapsibleState.None, 'token-count'));
                items.push(new OverviewItem(`📏 ${tokens.spacing?.length || 0} Spacing`, 'Design token spacing values', vscode.TreeItemCollapsibleState.None, 'token-count'));
                items.push(new OverviewItem(`🔤 ${tokens.typography?.length || 0} Typography`, 'Design token typography', vscode.TreeItemCollapsibleState.None, 'token-count'));
                items.push(new OverviewItem(`🔲 ${tokens.borderRadius?.length || 0} Border Radius`, 'Design token border radius', vscode.TreeItemCollapsibleState.None, 'token-count'));
                items.push(new OverviewItem(`⚛️ ${Object.keys(tokens.components || {}).length} Components`, 'Registered components', vscode.TreeItemCollapsibleState.None, 'token-count'));
            }
            catch (error) {
                items.push(new OverviewItem('❌ Invalid Tokens File', 'tokens.json contains invalid JSON', vscode.TreeItemCollapsibleState.None, 'error'));
            }
        }
        else {
            items.push(new OverviewItem('⚠️ Not Initialized', 'Click to initialize design tokens', vscode.TreeItemCollapsibleState.None, 'warning', {
                command: 'aiStethic.initializeTokens',
                title: 'Initialize Tokens'
            }));
        }
        // Quick actions
        items.push(new OverviewItem('🚀 Quick Actions', '', vscode.TreeItemCollapsibleState.Expanded, 'section'));
        return items;
    }
}
exports.OverviewProvider = OverviewProvider;
class OverviewItem extends vscode.TreeItem {
    constructor(label, tooltip, collapsibleState, contextValue, command) {
        super(label, collapsibleState);
        this.label = label;
        this.tooltip = tooltip;
        this.collapsibleState = collapsibleState;
        this.contextValue = contextValue;
        this.command = command;
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
    }
}
exports.OverviewItem = OverviewItem;
//# sourceMappingURL=overview-provider.js.map