{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,4BAmNC;AAqGD,gCAEC;AAjUD,+CAAiC;AACjC,2CAA6B;AAE7B,iEAA6D;AAC7D,qEAAiE;AACjE,6DAAyD;AAEzD,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAEtD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,IAAI,oCAAgB,EAAE,CAAC;IAChD,MAAM,kBAAkB,GAAG,IAAI,wCAAkB,EAAE,CAAC;IACpD,MAAM,cAAc,GAAG,IAAI,gCAAc,EAAE,CAAC;IAE5C,+BAA+B;IAC/B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,oBAAoB,EAAE;QAC/C,gBAAgB,EAAE,gBAAgB;QAClC,eAAe,EAAE,KAAK;KACzB,CAAC,CAAC;IAEH,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE;QACjD,gBAAgB,EAAE,kBAAkB;QACpC,eAAe,EAAE,IAAI;KACxB,CAAC,CAAC;IAEH,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,kBAAkB,EAAE;QAC7C,gBAAgB,EAAE,cAAc;QAChC,eAAe,EAAE,IAAI;KACxB,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACrG,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+CAA+C,CAAC,CAAC;YAChF,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,8BAA8B;YAC9B,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAEvD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,MAAM,aAAa,GAAG;gBAClB,MAAM,EAAE;oBACJ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;oBACpE,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;iBACzE;gBACD,OAAO,EAAE;oBACL,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;oBACpE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;iBACzE;gBACD,UAAU,EAAE;oBACR,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE;iBAC/E;gBACD,YAAY,EAAE;oBACV,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE;iBACjF;gBACD,UAAU,EAAE,EAAE;aACjB,CAAC;YAEF,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qDAAqD,CAAC,CAAC;YAE5F,gBAAgB;YAChB,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC3B,cAAc,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAEpD,MAAM,yBAAyB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;QACzG,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+CAA+C,CAAC,CAAC;YAChF,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;YACzE,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC/B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,kCAAkC,YAAY,EAAE,CAAC,CAAC;gBACnF,OAAO;YACX,CAAC;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,KAAK;iBACnB,MAAM,CAAC,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;iBACxE,GAAG,CAAC,CAAC,IAAY,EAAE,EAAE;gBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrD,OAAO;oBACH,IAAI;oBACJ,UAAU,EAAE,uBAAuB,IAAI,EAAE;oBACzC,aAAa,EAAE,EAAE;oBACjB,eAAe,EAAE,EAAE;iBACtB,CAAC;YACN,CAAC,CAAC,CAAC;YAEP,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,UAAU,CAAC,MAAM,gBAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpI,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IAEtD,MAAM,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QACrG,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC/C,MAAM,EAAE,8BAA8B;YACtC,WAAW,EAAE,yCAAyC;SACzD,CAAC,CAAC;QAEH,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC/D,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,SAAS,IAAI,EAAE;aAC7B,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAEtB,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;YAChD,IAAI,YAAY,EAAE,CAAC;gBACf,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAC5B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;gBACH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8CAA8C,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YAClB,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAChC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mFAAmF,CAAC,CAAC;YACxH,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uBAAuB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC;QACL,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAEpD,iCAAiC;IACjC,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2BAA2B,EAAE,GAAG,EAAE;QAC7F,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC3B,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAC7B,cAAc,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAEnD,MAAM,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;QACjG,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+CAA+C,CAAC,CAAC;YAChF,OAAO;QACX,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC9E,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAEzB,IAAI,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACzE,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iDAAiD,CAAC,CAAC;QACtF,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAElD,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,CAAC,aAAkB,EAAE,EAAE;QACzG,IAAI,aAAa,CAAC,GAAG,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE;gBAC9C,SAAS,EAAE,aAAa,CAAC,UAAU,CAAC,KAAK;aAC5C,CAAC,CAAC;QACP,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAEhD,kCAAkC;IAClC,MAAM,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;IACvF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAEjD,gDAAgD;IAChD,MAAM,sBAAsB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC/E,iBAAiB,CAAC,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAEnD,oCAAoC;IACpC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACzC,iBAAiB,CAAC,GAAG,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,8DAA8D,EAC9D,mBAAmB,CACtB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QACf,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;YACpC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;QACjE,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,iBAAiB,CAAC,QAA6B,EAAE,UAAuC,EAAE,kBAAsC;IACrI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;QAChC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO;IACX,CAAC;IAED,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC;IACzE,IAAI,CAAC,aAAa;QAAE,OAAO;IAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;IAC9E,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;QACjC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO;IACX,CAAC;IAED,IAAI,CAAC;QACD,MAAM,aAAa,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACzC,MAAM,WAAW,GAAwB,EAAE,CAAC;QAE5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,4CAA4C;QAC5C,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,IAAI,QAAQ,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAClE,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;gBAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACtD,IAAI,YAAY,EAAE,CAAC;oBACf,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACzB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CACnD,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAClD,CAAC;wBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;4BAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;4BACrC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,SAAS,EAAE,QAAQ,EACnB,SAAS,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,CACrC,CAAC;4BAEF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,UAAU,KAAK,iEAAiE,EAChF,MAAM,CAAC,kBAAkB,CAAC,OAAO,CACpC,CAAC;4BACF,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;4BACjC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACjC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAClD,IAAI,cAAc,EAAE,CAAC;oBACjB,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;wBACnD,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CACpD,KAAK,CAAC,MAAM,KAAK,MAAM,CAC1B,CAAC;wBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;4BAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;4BACvC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,SAAS,EAAE,QAAQ,EACnB,SAAS,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,CACvC,CAAC;4BAEF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,YAAY,OAAO,iEAAiE,EACpF,MAAM,CAAC,kBAAkB,CAAC,OAAO,CACpC,CAAC;4BACF,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;4BACjC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACjC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QAED,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAE1C,6BAA6B;QAC7B,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IACnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;QAChD,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IAC1D,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,QAA6B;IACrD,MAAM,kBAAkB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IACjF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;QAChD,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC;AAC1C,CAAC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;AAC5D,CAAC"}