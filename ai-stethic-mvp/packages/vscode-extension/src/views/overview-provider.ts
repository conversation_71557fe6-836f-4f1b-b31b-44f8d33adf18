import * as vscode from 'vscode';
import * as path from 'path';

export class OverviewProvider implements vscode.TreeDataProvider<OverviewItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<OverviewItem | undefined | null | void> = new vscode.EventEmitter<OverviewItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<OverviewItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor() {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: OverviewItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: OverviewItem): Thenable<OverviewItem[]> {
        if (!element) {
            return Promise.resolve(this.getOverviewItems());
        }
        return Promise.resolve([]);
    }

    private getOverviewItems(): OverviewItem[] {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return [
                new OverviewItem(
                    'No Workspace Open',
                    'Open a workspace to use AI-Stethic',
                    vscode.TreeItemCollapsibleState.None,
                    'warning'
                )
            ];
        }

        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        const hasTokens = fs.existsSync(tokensFilePath);

        const items: OverviewItem[] = [];

        // Status item
        if (hasTokens) {
            try {
                const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
                const tokens = JSON.parse(tokensContent);
                
                items.push(new OverviewItem(
                    '✅ AI-Stethic Active',
                    'Design system enforcement is running',
                    vscode.TreeItemCollapsibleState.None,
                    'status-active'
                ));

                // Token counts
                items.push(new OverviewItem(
                    `🎨 ${tokens.colors?.length || 0} Colors`,
                    'Design token colors',
                    vscode.TreeItemCollapsibleState.None,
                    'token-count'
                ));

                items.push(new OverviewItem(
                    `📏 ${tokens.spacing?.length || 0} Spacing`,
                    'Design token spacing values',
                    vscode.TreeItemCollapsibleState.None,
                    'token-count'
                ));

                items.push(new OverviewItem(
                    `🔤 ${tokens.typography?.length || 0} Typography`,
                    'Design token typography',
                    vscode.TreeItemCollapsibleState.None,
                    'token-count'
                ));

                items.push(new OverviewItem(
                    `🔲 ${tokens.borderRadius?.length || 0} Border Radius`,
                    'Design token border radius',
                    vscode.TreeItemCollapsibleState.None,
                    'token-count'
                ));

                items.push(new OverviewItem(
                    `⚛️ ${Object.keys(tokens.components || {}).length} Components`,
                    'Registered components',
                    vscode.TreeItemCollapsibleState.None,
                    'token-count'
                ));

            } catch (error) {
                items.push(new OverviewItem(
                    '❌ Invalid Tokens File',
                    'tokens.json contains invalid JSON',
                    vscode.TreeItemCollapsibleState.None,
                    'error'
                ));
            }
        } else {
            items.push(new OverviewItem(
                '⚠️ Not Initialized',
                'Click to initialize design tokens',
                vscode.TreeItemCollapsibleState.None,
                'warning',
                {
                    command: 'aiStethic.initializeTokens',
                    title: 'Initialize Tokens'
                }
            ));
        }

        // Quick actions
        items.push(new OverviewItem(
            '🚀 Quick Actions',
            '',
            vscode.TreeItemCollapsibleState.Expanded,
            'section'
        ));

        return items;
    }
}

export class OverviewItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly tooltip: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly contextValue: string,
        public readonly command?: vscode.Command
    ) {
        super(label, collapsibleState);
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
    }
}
