import * as vscode from 'vscode';
import * as path from 'path';

export class TokensProvider implements vscode.TreeDataProvider<TokenItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<TokenItem | undefined | null | void> = new vscode.EventEmitter<TokenItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<TokenItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor() {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: TokenItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: TokenItem): Thenable<TokenItem[]> {
        if (!element) {
            return Promise.resolve(this.getTokenCategories());
        } else if (element.contextValue === 'category') {
            return Promise.resolve(this.getTokensForCategory(element.category!));
        }
        return Promise.resolve([]);
    }

    private getTokenCategories(): TokenItem[] {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            return [
                new TokenItem(
                    'No Workspace Open',
                    'Open a workspace to view tokens',
                    vscode.TreeItemCollapsibleState.None,
                    'no-workspace'
                )
            ];
        }

        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        
        if (!fs.existsSync(tokensFilePath)) {
            return [
                new TokenItem(
                    'No Tokens File',
                    'Initialize design tokens to get started',
                    vscode.TreeItemCollapsibleState.None,
                    'no-tokens',
                    undefined,
                    {
                        command: 'aiStethic.initializeTokens',
                        title: 'Initialize Tokens'
                    }
                )
            ];
        }

        try {
            const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
            const tokens = JSON.parse(tokensContent);

            const categories: TokenItem[] = [];

            if (tokens.colors && tokens.colors.length > 0) {
                categories.push(new TokenItem(
                    `🎨 Colors (${tokens.colors.length})`,
                    'Color design tokens',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    'category',
                    'colors'
                ));
            }

            if (tokens.spacing && tokens.spacing.length > 0) {
                categories.push(new TokenItem(
                    `📏 Spacing (${tokens.spacing.length})`,
                    'Spacing design tokens',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    'category',
                    'spacing'
                ));
            }

            if (tokens.typography && tokens.typography.length > 0) {
                categories.push(new TokenItem(
                    `🔤 Typography (${tokens.typography.length})`,
                    'Typography design tokens',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    'category',
                    'typography'
                ));
            }

            if (tokens.borderRadius && tokens.borderRadius.length > 0) {
                categories.push(new TokenItem(
                    `🔲 Border Radius (${tokens.borderRadius.length})`,
                    'Border radius design tokens',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    'category',
                    'borderRadius'
                ));
            }

            if (tokens.components && Object.keys(tokens.components).length > 0) {
                categories.push(new TokenItem(
                    `⚛️ Components (${Object.keys(tokens.components).length})`,
                    'Registered components',
                    vscode.TreeItemCollapsibleState.Collapsed,
                    'category',
                    'components'
                ));
            }

            if (categories.length === 0) {
                categories.push(new TokenItem(
                    'No Tokens Defined',
                    'Add some design tokens to get started',
                    vscode.TreeItemCollapsibleState.None,
                    'empty'
                ));
            }

            return categories;

        } catch (error) {
            return [
                new TokenItem(
                    'Invalid Tokens File',
                    'tokens.json contains invalid JSON',
                    vscode.TreeItemCollapsibleState.None,
                    'error'
                )
            ];
        }
    }

    private getTokensForCategory(category: string): TokenItem[] {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) return [];

        const fs = require('fs');
        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        
        try {
            const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
            const tokens = JSON.parse(tokensContent);
            const items: TokenItem[] = [];

            switch (category) {
                case 'colors':
                    tokens.colors?.forEach((token: any) => {
                        items.push(new TokenItem(
                            `${token.name}`,
                            `${token.hex} - ${token.value}`,
                            vscode.TreeItemCollapsibleState.None,
                            'color-token',
                            undefined,
                            undefined,
                            token
                        ));
                    });
                    break;

                case 'spacing':
                    tokens.spacing?.forEach((token: any) => {
                        items.push(new TokenItem(
                            `${token.name}`,
                            `${token.value} (${token.pixels}px)`,
                            vscode.TreeItemCollapsibleState.None,
                            'spacing-token',
                            undefined,
                            undefined,
                            token
                        ));
                    });
                    break;

                case 'typography':
                    tokens.typography?.forEach((token: any) => {
                        const description = token.fontFamily || token.fontSize || token.value;
                        items.push(new TokenItem(
                            `${token.name}`,
                            description,
                            vscode.TreeItemCollapsibleState.None,
                            'typography-token',
                            undefined,
                            undefined,
                            token
                        ));
                    });
                    break;

                case 'borderRadius':
                    tokens.borderRadius?.forEach((token: any) => {
                        items.push(new TokenItem(
                            `${token.name}`,
                            `${token.value} (${token.pixels}px)`,
                            vscode.TreeItemCollapsibleState.None,
                            'border-radius-token',
                            undefined,
                            undefined,
                            token
                        ));
                    });
                    break;

                case 'components':
                    Object.entries(tokens.components || {}).forEach(([name, component]: [string, any]) => {
                        const requiredProps = component.requiredProps?.length || 0;
                        const requiredClasses = component.requiredClasses?.length || 0;
                        items.push(new TokenItem(
                            `${name}`,
                            `${component.importPath} (${requiredProps} props, ${requiredClasses} classes)`,
                            vscode.TreeItemCollapsibleState.None,
                            'component-token',
                            undefined,
                            undefined,
                            component
                        ));
                    });
                    break;
            }

            return items;

        } catch (error) {
            return [
                new TokenItem(
                    'Error Loading Tokens',
                    'Failed to parse tokens file',
                    vscode.TreeItemCollapsibleState.None,
                    'error'
                )
            ];
        }
    }
}

export class TokenItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly tooltip: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly contextValue: string,
        public readonly category?: string,
        public readonly command?: vscode.Command,
        public readonly tokenData?: any
    ) {
        super(label, collapsibleState);
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
        
        // Add color preview for color tokens
        if (contextValue === 'color-token' && tokenData?.hex) {
            this.iconPath = new vscode.ThemeIcon('circle-filled', new vscode.ThemeColor('charts.red'));
        }
    }
}
