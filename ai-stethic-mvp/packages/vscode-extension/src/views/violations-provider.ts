import * as vscode from 'vscode';

export class ViolationsProvider implements vscode.TreeDataProvider<ViolationItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ViolationItem | undefined | null | void> = new vscode.EventEmitter<ViolationItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ViolationItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private violations: Map<string, vscode.Diagnostic[]> = new Map();

    constructor() {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    updateViolations(uri: vscode.Uri, diagnostics: vscode.Diagnostic[]): void {
        if (diagnostics.length === 0) {
            this.violations.delete(uri.toString());
        } else {
            this.violations.set(uri.toString(), diagnostics);
        }
        this.refresh();
    }

    getTreeItem(element: ViolationItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ViolationItem): Thenable<ViolationItem[]> {
        if (!element) {
            return Promise.resolve(this.getViolationItems());
        }
        return Promise.resolve([]);
    }

    private getViolationItems(): ViolationItem[] {
        const items: ViolationItem[] = [];
        
        if (this.violations.size === 0) {
            items.push(new ViolationItem(
                '✅ No Violations Found',
                'Your code follows the design system!',
                vscode.TreeItemCollapsibleState.None,
                'no-violations'
            ));
            return items;
        }

        let totalViolations = 0;
        for (const [uriString, diagnostics] of this.violations) {
            const uri = vscode.Uri.parse(uriString);
            const fileName = uri.path.split('/').pop() || 'Unknown';
            
            totalViolations += diagnostics.length;

            // Group violations by file
            const fileItem = new ViolationItem(
                `📄 ${fileName} (${diagnostics.length})`,
                `${diagnostics.length} violation(s) in ${fileName}`,
                vscode.TreeItemCollapsibleState.Expanded,
                'file-violations'
            );
            items.push(fileItem);

            // Add individual violations
            diagnostics.forEach((diagnostic, index) => {
                const line = diagnostic.range.start.line + 1;
                const violationType = this.getViolationType(diagnostic.message);
                const icon = this.getViolationIcon(violationType);
                
                const violationItem = new ViolationItem(
                    `${icon} Line ${line}: ${this.shortenMessage(diagnostic.message)}`,
                    diagnostic.message,
                    vscode.TreeItemCollapsibleState.None,
                    'violation',
                    {
                        command: 'vscode.open',
                        title: 'Open File',
                        arguments: [uri, { selection: diagnostic.range }]
                    }
                );
                
                violationItem.uri = uri;
                violationItem.diagnostic = diagnostic;
                items.push(violationItem);
            });
        }

        // Add summary at the top
        const summaryItem = new ViolationItem(
            `⚠️ ${totalViolations} Total Violations`,
            `Found ${totalViolations} design system violations across ${this.violations.size} files`,
            vscode.TreeItemCollapsibleState.None,
            'summary'
        );
        items.unshift(summaryItem);

        return items;
    }

    private getViolationType(message: string): string {
        if (message.includes('Color')) return 'color';
        if (message.includes('Spacing')) return 'spacing';
        if (message.includes('Typography')) return 'typography';
        if (message.includes('Border')) return 'border';
        return 'unknown';
    }

    private getViolationIcon(type: string): string {
        switch (type) {
            case 'color': return '🎨';
            case 'spacing': return '📏';
            case 'typography': return '🔤';
            case 'border': return '🔲';
            default: return '⚠️';
        }
    }

    private shortenMessage(message: string): string {
        // Shorten long messages for tree view
        if (message.length > 50) {
            return message.substring(0, 47) + '...';
        }
        return message;
    }

    getTotalViolations(): number {
        let total = 0;
        for (const diagnostics of this.violations.values()) {
            total += diagnostics.length;
        }
        return total;
    }

    getViolationsByFile(): Map<string, vscode.Diagnostic[]> {
        return this.violations;
    }
}

export class ViolationItem extends vscode.TreeItem {
    public uri?: vscode.Uri;
    public diagnostic?: vscode.Diagnostic;

    constructor(
        public readonly label: string,
        public readonly tooltip: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly contextValue: string,
        public readonly command?: vscode.Command
    ) {
        super(label, collapsibleState);
        this.tooltip = tooltip;
        this.contextValue = contextValue;
        this.command = command;
    }
}
