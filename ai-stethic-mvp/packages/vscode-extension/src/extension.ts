import * as vscode from 'vscode';
import * as path from 'path';
import { DesignLinter } from './design-linter';
import { OverviewProvider } from './views/overview-provider';
import { ViolationsProvider } from './views/violations-provider';
import { TokensProvider } from './views/tokens-provider';

export function activate(context: vscode.ExtensionContext) {
    console.log('🎨 AI-Stethic extension is now active!');

    // Initialize view providers
    const overviewProvider = new OverviewProvider();
    const violationsProvider = new ViolationsProvider();
    const tokensProvider = new TokensProvider();

    // Register tree view providers
    vscode.window.createTreeView('aiStethic.overview', {
        treeDataProvider: overviewProvider,
        showCollapseAll: false
    });

    vscode.window.createTreeView('aiStethic.violations', {
        treeDataProvider: violationsProvider,
        showCollapseAll: true
    });

    vscode.window.createTreeView('aiStethic.tokens', {
        treeDataProvider: tokensProvider,
        showCollapseAll: true
    });

    // Register commands
    const initializeTokensCommand = vscode.commands.registerCommand('aiStethic.initializeTokens', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        try {
            // Simple token initialization
            const fs = require('fs');
            const tokensDir = path.join(workspaceRoot, '.ai-stethic');
            const tokensFile = path.join(tokensDir, 'tokens.json');

            if (!fs.existsSync(tokensDir)) {
                fs.mkdirSync(tokensDir, { recursive: true });
            }

            const defaultTokens = {
                colors: [
                    { name: "primary", value: "#3b82f6", type: "color", hex: "#3b82f6" },
                    { name: "secondary", value: "#64748b", type: "color", hex: "#64748b" }
                ],
                spacing: [
                    { name: "sm", value: "8px", type: "spacing", pixels: 8, unit: "px" },
                    { name: "md", value: "16px", type: "spacing", pixels: 16, unit: "px" }
                ],
                typography: [
                    { name: "heading", value: "Inter", type: "typography", fontFamily: "Inter" }
                ],
                borderRadius: [
                    { name: "rounded", value: "4px", type: "borderRadius", pixels: 4, unit: "px" }
                ],
                components: {}
            };

            fs.writeFileSync(tokensFile, JSON.stringify(defaultTokens, null, 2));
            vscode.window.showInformationMessage('AI-Stethic: Design tokens initialized successfully!');

            // Refresh views
            overviewProvider.refresh();
            tokensProvider.refresh();
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to initialize tokens: ${error}`);
        }
    });
    context.subscriptions.push(initializeTokensCommand);

    const registerComponentsCommand = vscode.commands.registerCommand('aiStethic.registerComponents', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        try {
            const componentDir = path.join(workspaceRoot, 'src', 'components', 'ui');
            const fs = require('fs');

            if (!fs.existsSync(componentDir)) {
                vscode.window.showWarningMessage(`Component directory not found: ${componentDir}`);
                return;
            }

            const files = fs.readdirSync(componentDir);
            const components = files
                .filter((file: string) => file.endsWith('.tsx') || file.endsWith('.jsx'))
                .map((file: string) => {
                    const name = path.basename(file, path.extname(file));
                    return {
                        name,
                        importPath: `./src/components/ui/${file}`,
                        requiredProps: [],
                        requiredClasses: []
                    };
                });

            vscode.window.showInformationMessage(`Found ${components.length} components: ${components.map((c: any) => c.name).join(', ')}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to scan components: ${error}`);
        }
    });
    context.subscriptions.push(registerComponentsCommand);

    const enhanceComponentCommand = vscode.commands.registerCommand('aiStethic.enhanceComponent', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found.');
            return;
        }

        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }

        const selectedCode = editor.document.getText(selection);
        const styleHint = await vscode.window.showInputBox({
            prompt: 'Enter an optional style hint',
            placeHolder: 'e.g., "make it modern", "add dark mode"'
        });

        try {
            const axios = require('axios');
            const response = await axios.post('http://localhost:8000/enhance', {
                code: selectedCode,
                styleHint: styleHint || ''
            }, { timeout: 5000 });

            const enhancedCode = response.data.enhancedCode;
            if (enhancedCode) {
                await editor.edit(editBuilder => {
                    editBuilder.replace(selection, enhancedCode);
                });
                vscode.window.showInformationMessage('AI-Stethic: Component enhanced successfully!');
            }
        } catch (error: any) {
            if (error.code === 'ECONNREFUSED') {
                vscode.window.showErrorMessage('AI-Stethic: Enhancement API is not running. Please start the stub backend server.');
            } else {
                vscode.window.showErrorMessage(`Enhancement failed: ${error.message}`);
            }
        }
    });
    context.subscriptions.push(enhanceComponentCommand);

    // Register view-related commands
    const refreshOverviewCommand = vscode.commands.registerCommand('aiStethic.refreshOverview', () => {
        overviewProvider.refresh();
        violationsProvider.refresh();
        tokensProvider.refresh();
    });
    context.subscriptions.push(refreshOverviewCommand);

    const openTokensFileCommand = vscode.commands.registerCommand('aiStethic.openTokensFile', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
        const fs = require('fs');

        if (fs.existsSync(tokensFilePath)) {
            const document = await vscode.workspace.openTextDocument(tokensFilePath);
            await vscode.window.showTextDocument(document);
        } else {
            vscode.window.showErrorMessage('Tokens file not found. Initialize tokens first.');
        }
    });
    context.subscriptions.push(openTokensFileCommand);

    const fixViolationCommand = vscode.commands.registerCommand('aiStethic.fixViolation', (violationItem: any) => {
        if (violationItem.uri && violationItem.diagnostic) {
            vscode.window.showTextDocument(violationItem.uri, {
                selection: violationItem.diagnostic.range
            });
        }
    });
    context.subscriptions.push(fixViolationCommand);

    // Add basic linting functionality
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('ai-stethic');
    context.subscriptions.push(diagnosticCollection);

    // Register document change listener for linting
    const documentChangeListener = vscode.workspace.onDidSaveTextDocument((document) => {
        updateDiagnostics(document, diagnosticCollection, violationsProvider);
    });
    context.subscriptions.push(documentChangeListener);

    // Lint all open documents initially
    vscode.workspace.textDocuments.forEach(doc => {
        updateDiagnostics(doc, diagnosticCollection, violationsProvider);
    });

    // Show welcome message
    vscode.window.showInformationMessage(
        'AI-Stethic is ready! Use the command palette to get started.',
        'Initialize Tokens'
    ).then(selection => {
        if (selection === 'Initialize Tokens') {
            vscode.commands.executeCommand('aiStethic.initializeTokens');
        }
    });
}

function updateDiagnostics(document: vscode.TextDocument, collection: vscode.DiagnosticCollection, violationsProvider: ViolationsProvider): void {
    if (!shouldLintDocument(document)) {
        collection.delete(document.uri);
        return;
    }

    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) return;

    const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
    const fs = require('fs');

    if (!fs.existsSync(tokensFilePath)) {
        collection.delete(document.uri);
        return;
    }

    try {
        const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
        const tokens = JSON.parse(tokensContent);
        const diagnostics: vscode.Diagnostic[] = [];

        const content = document.getText();
        const lines = content.split('\n');

        // Simple color detection for CSS/SCSS files
        if (document.languageId === 'css' || document.languageId === 'scss') {
            lines.forEach((line, lineIndex) => {
                const colorMatches = line.match(/#[0-9a-fA-F]{3,6}/g);
                if (colorMatches) {
                    colorMatches.forEach(color => {
                        const isValidToken = tokens.colors.some((token: any) =>
                            token.hex.toLowerCase() === color.toLowerCase()
                        );

                        if (!isValidToken) {
                            const startPos = line.indexOf(color);
                            const range = new vscode.Range(
                                lineIndex, startPos,
                                lineIndex, startPos + color.length
                            );

                            const diagnostic = new vscode.Diagnostic(
                                range,
                                `Color "${color}" is not in your design tokens. Consider using a token instead.`,
                                vscode.DiagnosticSeverity.Warning
                            );
                            diagnostic.source = 'AI-Stethic';
                            diagnostics.push(diagnostic);
                        }
                    });
                }

                // Simple spacing detection
                const spacingMatches = line.match(/\b(\d+)px\b/g);
                if (spacingMatches) {
                    spacingMatches.forEach(spacing => {
                        const pixels = parseInt(spacing.replace('px', ''));
                        const isValidToken = tokens.spacing.some((token: any) =>
                            token.pixels === pixels
                        );

                        if (!isValidToken) {
                            const startPos = line.indexOf(spacing);
                            const range = new vscode.Range(
                                lineIndex, startPos,
                                lineIndex, startPos + spacing.length
                            );

                            const diagnostic = new vscode.Diagnostic(
                                range,
                                `Spacing "${spacing}" is not in your design tokens. Consider using a token instead.`,
                                vscode.DiagnosticSeverity.Warning
                            );
                            diagnostic.source = 'AI-Stethic';
                            diagnostics.push(diagnostic);
                        }
                    });
                }
            });
        }

        collection.set(document.uri, diagnostics);

        // Update violations provider
        violationsProvider.updateViolations(document.uri, diagnostics);
    } catch (error) {
        console.error('Error linting document:', error);
        collection.delete(document.uri);
        violationsProvider.updateViolations(document.uri, []);
    }
}

function shouldLintDocument(document: vscode.TextDocument): boolean {
    const supportedLanguages = ['css', 'scss', 'javascriptreact', 'typescriptreact'];
    return supportedLanguages.includes(document.languageId) &&
           document.uri.scheme === 'file';
}

export function deactivate() {
    console.log('AI-Stethic extension is now deactivated.');
}
